import * as CryptoJS from 'crypto-js';

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
}

export interface ValidationResult {
  isValid: boolean;
  user?: TelegramUser;
  error?: string;
}

/**
 * Validates Telegram Web App init data using the bot token
 * Based on Telegram's validation algorithm
 */
export function validateTelegramWebAppData(
  initData: string,
  botToken: string
): ValidationResult {
  try {
    // Parse the init data
    const urlParams = new URLSearchParams(initData);
    const hash = urlParams.get('hash');
    
    if (!hash) {
      return { isValid: false, error: 'Hash is missing' };
    }

    // Remove hash from params for validation
    urlParams.delete('hash');
    
    // Sort parameters alphabetically and create data check string
    const dataCheckArray: string[] = [];
    for (const [key, value] of Array.from(urlParams.entries()).sort()) {
      dataCheckArray.push(`${key}=${value}`);
    }
    const dataCheckString = dataCheckArray.join('\n');

    // Create secret key using bot token
    const secretKey = CryptoJS.HmacSHA256(botToken, 'WebAppData');
    
    // Calculate expected hash
    const expectedHash = CryptoJS.HmacSHA256(dataCheckString, secretKey).toString();

    // Verify hash
    if (hash !== expectedHash) {
      return { isValid: false, error: 'Invalid hash' };
    }

    // Check auth_date (should not be older than 24 hours)
    const authDate = urlParams.get('auth_date');
    if (!authDate) {
      return { isValid: false, error: 'Auth date is missing' };
    }

    const authTimestamp = parseInt(authDate, 10);
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const maxAge = 24 * 60 * 60; // 24 hours in seconds

    if (currentTimestamp - authTimestamp > maxAge) {
      return { isValid: false, error: 'Auth data is too old' };
    }

    // Parse user data
    const userParam = urlParams.get('user');
    if (!userParam) {
      return { isValid: false, error: 'User data is missing' };
    }

    const user: TelegramUser = JSON.parse(userParam);

    return { isValid: true, user };
  } catch (error) {
    return { 
      isValid: false, 
      error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}
