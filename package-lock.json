{"name": "marketplace-functions", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"@twa-dev/sdk": "^8.0.2", "@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0"}}, "node_modules/@twa-dev/sdk": {"version": "8.0.2", "resolved": "https://registry.npmjs.org/@twa-dev/sdk/-/sdk-8.0.2.tgz", "integrity": "sha512-Pp5GxnxP2blboVZFiM9aWjs4cb8IpW3x2jP3kLOMvIqy0jzNUTuFHkwHtx+zEvh/UcF2F+wmS8G6ebIA0XPXcg==", "license": "MIT", "dependencies": {"@twa-dev/types": "^8.0.1"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0"}}, "node_modules/@twa-dev/types": {"version": "8.0.2", "resolved": "https://registry.npmjs.org/@twa-dev/types/-/types-8.0.2.tgz", "integrity": "sha512-ICQ6n4NaUPPzV3/GzflVQS6Nnu5QX2vr9OlOG8ZkFf3rSJXzRKazrLAbZlVhCPPWkIW3MMuELPsE6tByrA49qA==", "license": "MIT"}, "node_modules/@types/crypto-js": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/@types/crypto-js/-/crypto-js-4.2.2.tgz", "integrity": "sha512-sDOLlVbHhXpAUAL0YHDUUwDZf3iN4Bwi4W6a0W0b+QcAezUbRtH4FVb+9J4h+XFPW7l/gQ9F8qC7P+Ec4k8QVQ==", "license": "MIT"}, "node_modules/crypto-js": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz", "integrity": "sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==", "license": "MIT"}, "node_modules/react": {"version": "19.1.0", "resolved": "https://registry.npmjs.org/react/-/react-19.1.0.tgz", "integrity": "sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}}}