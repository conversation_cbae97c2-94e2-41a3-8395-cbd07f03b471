"use client";

import { useEffect, useState } from "react";
import { useRootContext } from "@/root-context";
import { 
  isTelegramWebApp, 
  initTelegramWebApp, 
  getTelegramWebAppData 
} from "@/utils/telegram-auth";

export const TelegramTest = () => {
  const { currentUser } = useRootContext();
  const [telegramData, setTelegramData] = useState<any>(null);
  const [isInTelegram, setIsInTelegram] = useState(false);

  useEffect(() => {
    // Check if we're in Telegram
    const checkTelegram = () => {
      const inTelegram = isTelegramWebApp();
      setIsInTelegram(inTelegram);
      
      if (inTelegram) {
        // Initialize Telegram Web App
        initTelegramWebApp();
        
        // Get Telegram data
        const data = getTelegramWebAppData();
        setTelegramData(data);
      }
    };

    checkTelegram();
    
    // Check again after a delay to ensure Telegram script is loaded
    const timer = setTimeout(checkTelegram, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="p-6 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Telegram Integration Test</h3>
      
      <div className="space-y-4">
        <div>
          <strong>Is in Telegram Web App:</strong> {isInTelegram ? "Yes" : "No"}
        </div>
        
        <div>
          <strong>Current Firebase User:</strong> {currentUser ? currentUser.uid : "Not signed in"}
        </div>
        
        {telegramData && (
          <div>
            <strong>Telegram Data:</strong>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
              {JSON.stringify(telegramData, null, 2)}
            </pre>
          </div>
        )}
        
        {typeof window !== 'undefined' && window.Telegram?.WebApp && (
          <div>
            <strong>Telegram WebApp Object:</strong>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
              {JSON.stringify({
                initData: window.Telegram.WebApp.initData,
                initDataUnsafe: window.Telegram.WebApp.initDataUnsafe,
              }, null, 2)}
            </pre>
          </div>
        )}
        
        <div className="text-sm text-gray-600">
          <p>
            <strong>Note:</strong> This component shows debug information about Telegram Web App integration.
            It will only show meaningful data when opened from within Telegram.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TelegramTest;
