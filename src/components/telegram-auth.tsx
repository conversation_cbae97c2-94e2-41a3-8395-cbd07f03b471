"use client";

import { useEffect, useState } from "react";
import { signInWithCustomToken } from "firebase/auth";
import { httpsCallable } from "firebase/functions";

import { firebaseAuth, firebaseFunctions } from "@/root-context";
import {
  getTelegramWebAppData,
  initTelegramWebApp,
  isTelegramWebApp,
} from "@/utils/telegram-auth";

interface TelegramAuthProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const TelegramAuth = ({ onSuccess, onError }: TelegramAuthProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isTelegramApp, setIsTelegramApp] = useState(false);

  useEffect(() => {
    // Check if we're in Telegram Web App
    const checkTelegramApp = () => {
      const isInTelegram = isTelegramWebApp();
      setIsTelegramApp(isInTelegram);
      
      if (isInTelegram) {
        // Initialize Telegram Web App
        initTelegramWebApp();
      }
    };

    // Check immediately and after a short delay to ensure Telegram script is loaded
    checkTelegramApp();
    const timer = setTimeout(checkTelegramApp, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleTelegramAuth = async () => {
    if (!isTelegramApp) {
      onError?.("This app must be opened from Telegram");
      return;
    }

    setIsLoading(true);

    try {
      // Get Telegram Web App data
      const telegramData = getTelegramWebAppData();
      
      if (!telegramData) {
        throw new Error("Failed to get Telegram data");
      }

      // Call Firebase function to authenticate
      const authenticateWithTelegram = httpsCallable(
        firebaseFunctions,
        "authenticateWithTelegram"
      );

      const result = await authenticateWithTelegram({
        initData: window.Telegram?.WebApp?.initData,
      });

      const { customToken } = result.data as { customToken: string };

      // Sign in with custom token
      await signInWithCustomToken(firebaseAuth, customToken);

      onSuccess?.();
    } catch (error) {
      console.error("Telegram authentication error:", error);
      onError?.(
        error instanceof Error ? error.message : "Authentication failed"
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!isTelegramApp) {
    return (
      <div className="text-center p-4">
        <p className="text-muted-foreground">
          This app is designed to work within Telegram.
          <br />
          Please open it from a Telegram bot or Web App.
        </p>
      </div>
    );
  }

  return (
    <div className="text-center p-4">
      <button
        onClick={handleTelegramAuth}
        disabled={isLoading}
        className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
      >
        {isLoading ? (
          <>
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            Signing in...
          </>
        ) : (
          "Sign in with Telegram"
        )}
      </button>
    </div>
  );
};

export default TelegramAuth;
